#ifndef SR_HPP
#define SR_HPP

#include "kernel_4_14_117.h"
#include "kernel_4_14_141.h"
#include "kernel_4_14_180.h"
#include "kernel_4_14_186.h"
#include "kernel_4_14_186b.h"
#include "kernel_4_14_186c.h"
#include "kernel_4_14_190.h"
#include "kernel_4_19_113.h"
#include "kernel_4_19_157.h"
#include "kernel_4_19_157b.h"
#include "kernel_4_19_157c.h"
#include "kernel_4_19_191.h"
#include "kernel_4_19_191b.h"
#include "kernel_4_19_191c.h"
#include "kernel_4_19_81.h"
#include "kernel_4_9_186.h"
#include "kernel_5_10.h"
#include "kernel_5_15.h"
#include "kernel_5_4.h"
#include "kernel_5_4b.h"
#include "kernel_6_1.h"
#include "kernel_6_6.h"
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <string>
#include <vector>
#include <unistd.h>

// 函数声明
inline void write_to_file(const char *filename, const unsigned char *data, unsigned int length);
inline bool insmodKernel(const char *path);
inline bool load_module(const unsigned char *data, unsigned int length, const char *name);
inline void get_kernel_version(char *version, size_t size);
inline std::string extract_base_version(const std::string& full_version);
inline std::vector<std::string> get_available_versions(const std::string& base_version);
inline bool load_driver_version(const std::string& version);

// 函数实现
inline void write_to_file(const char *filename, const unsigned char *data, unsigned int length)
{
    FILE *fp = fopen(filename, "wb");
    if (fp == nullptr)
    {
        std::cerr << "无法创建临时文件: " << filename << std::endl;
        exit(EXIT_FAILURE);
    }
    fwrite(data, 1, length, fp);
    fclose(fp);
}

inline bool insmodKernel(const char *path)
{
    std::string command = std::string("/system/bin/insmod ") + path + " 2>/dev/null";
    int status = system(command.c_str());
    if (status != 0) {
        std::cerr << "驱动安装失败" << std::endl;
        return false;
    }
    return true;
}

inline bool load_module(const unsigned char *data, unsigned int length, const char *name)
{
    const char *temp_file = "/data/local/tmp/canche.ko";
    write_to_file(temp_file, data, length);
    bool success = insmodKernel(temp_file);
    unlink(temp_file);
    return success;
}

inline void get_kernel_version(char *version, size_t size)
{
    FILE *fp = popen("uname -r", "r");
    if (fp == nullptr)
    {
        std::cerr << "无法执行uname命令获取内核版本" << std::endl;
        exit(EXIT_FAILURE);
    }
    if (fgets(version, size, fp) == nullptr)
    {
        pclose(fp);
        std::cerr << "读取内核版本失败" << std::endl;
        exit(EXIT_FAILURE);
    }
    version[strcspn(version, "\n")] = '\0';
    pclose(fp);
}

inline std::string extract_base_version(const std::string& full_version)
{
    size_t first_dot = full_version.find('.');
    if (first_dot == std::string::npos) return full_version;
    
    size_t second_dot = full_version.find('.', first_dot + 1);
    if (second_dot == std::string::npos) return full_version;
    
    size_t third_dot = full_version.find('.', second_dot + 1);
    if (third_dot != std::string::npos)
    {
        return full_version.substr(0, third_dot);
    }
    
    size_t non_digit = full_version.find_first_not_of("0123456789", second_dot + 1);
    if (non_digit != std::string::npos)
    {
        return full_version.substr(0, non_digit);
    }
    
    return full_version;
}

inline std::vector<std::string> get_available_versions(const std::string& base_version)
{
    std::vector<std::string> versions;
    
    if (base_version == "4.9") {
        versions = {"4.9.186"};
    } 
    else if (base_version == "4.14") {
        versions = {"4.14.117", "4.14.141", "4.14.180", "4.14.186", "4.14.186b", "4.14.186c", "4.14.190"};
    }
    else if (base_version == "4.19") {
        versions = {"4.19.81", "4.19.113", "4.19.157", "4.19.157b", "4.19.157c", 
                   "4.19.191", "4.19.191b", "4.19.191c"};
    }
    else if (base_version == "5.4") {
        versions = {"5.4", "5.4b"};
    }
    else if (base_version == "5.10") {
        versions = {"5.10"};
    }
    else if (base_version == "5.15") {
        versions = {"5.15"};
    }
    else if (base_version == "6.1") {
        versions = {"6.1"};
    }
    else if (base_version == "6.6") {
        versions = {"6.6"};
    }
    
    return versions;
}

inline bool load_driver_version(const std::string& version)
{
    bool success = false;
    if (version == "4.9.186") {
        success = load_module(kernel_4_9_186, sizeof(kernel_4_9_186), version.c_str());
    }
    else if (version == "4.14.117") {
        success = load_module(kernel_4_14_117, sizeof(kernel_4_14_117), version.c_str());
    }
    else if (version == "4.14.141") {
        success = load_module(kernel_4_14_141, sizeof(kernel_4_14_141), version.c_str());
    }
    else if (version == "4.14.180") {
        success = load_module(kernel_4_14_180, sizeof(kernel_4_14_180), version.c_str());
    }
    else if (version == "4.14.186") {
        success = load_module(kernel_4_14_186, sizeof(kernel_4_14_186), version.c_str());
    }
    else if (version == "4.14.186b") {
        success = load_module(kernel_4_14_186b, sizeof(kernel_4_14_186b), version.c_str());
    }
    else if (version == "4.14.186c") {
        success = load_module(kernel_4_14_186c, sizeof(kernel_4_14_186c), version.c_str());
    }
    else if (version == "4.14.190") {
        success = load_module(kernel_4_14_190, sizeof(kernel_4_14_190), version.c_str());
    }
    else if (version == "4.19.81") {
        success = load_module(kernel_4_19_81, sizeof(kernel_4_19_81), version.c_str());
    }
    else if (version == "4.19.113") {
        success = load_module(kernel_4_19_113, sizeof(kernel_4_19_113), version.c_str());
    }
    else if (version == "4.19.157") {
        success = load_module(kernel_4_19_157, sizeof(kernel_4_19_157), version.c_str());
    }
    else if (version == "4.19.157b") {
        success = load_module(kernel_4_19_157b, sizeof(kernel_4_19_157b), version.c_str());
    }
    else if (version == "4.19.157c") {
        success = load_module(kernel_4_19_157c, sizeof(kernel_4_19_157c), version.c_str());
    }
    else if (version == "4.19.191") {
        success = load_module(kernel_4_19_191, sizeof(kernel_4_19_191), version.c_str());
    }
    else if (version == "4.19.191b") {
        success = load_module(kernel_4_19_191b, sizeof(kernel_4_19_191b), version.c_str());
    }
    else if (version == "4.19.191c") {
        success = load_module(kernel_4_19_191c, sizeof(kernel_4_19_191c), version.c_str());
    }
    else if (version == "5.4") {
        success = load_module(kernel_5_4, sizeof(kernel_5_4), version.c_str());
    }
    else if (version == "5.4b") {
        success = load_module(kernel_5_4b, sizeof(kernel_5_4b), version.c_str());
    }
    else if (version == "5.10") {
        success = load_module(kernel_5_10, sizeof(kernel_5_10), version.c_str());
    }
    else if (version == "5.15") {
        success = load_module(kernel_5_15, sizeof(kernel_5_15), version.c_str());
    }
    else if (version == "6.1") {
        success = load_module(kernel_6_1, sizeof(kernel_6_1), version.c_str());
    }
    else if (version == "6.6") {
        success = load_module(kernel_6_6, sizeof(kernel_6_6), version.c_str());
    }
    else {
        std::cerr << "错误: 未知的驱动版本 " << version << std::endl;
        return false;
    }
    return success;
}

#endif // SR_HPP